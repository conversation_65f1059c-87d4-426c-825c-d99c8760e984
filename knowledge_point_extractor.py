#!/usr/bin/env python3
"""
Knowledge Point Extractor Script

This script reads FINAL.csv and extracts the first paragraph from knowledge point sources.
- For Wikipedia URLs: Uses Wikipedia API
- For other sources (like namu.wiki): Uses web scraping
- Adds extracted content to a new "Knowledge Point" column
"""

import pandas as pd
import requests
import re
from bs4 import BeautifulSoup
import time
import urllib.parse
from typing import Optional
import logging

# Set up logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class KnowledgePointExtractor:
    def __init__(self, csv_file_path: str):
        self.csv_file_path = csv_file_path
        self.session = requests.Session()
        self.session.headers.update(
            {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            }
        )

    def extract_wikipedia_content(self, url: str) -> Optional[str]:
        """Extract first paragraph from Wikipedia using API with fallback to web scraping"""
        try:
            # Extract page title from URL
            if "/wiki/" in url:
                page_title = url.split("/wiki/")[-1]
                page_title = urllib.parse.unquote(page_title)
            else:
                logger.warning(f"Could not extract page title from URL: {url}")
                return None

            # Determine language from URL
            if "en.wikipedia.org" in url:
                api_url = "https://en.wikipedia.org/api/rest_v1/page/summary/"
            elif "ko.wikipedia.org" in url:
                api_url = "https://ko.wikipedia.org/api/rest_v1/page/summary/"
            else:
                # Default to English
                api_url = "https://en.wikipedia.org/api/rest_v1/page/summary/"

            # Try API first with longer delay for rate limiting
            try:
                response = self.session.get(f"{api_url}{page_title}", timeout=10)
                response.raise_for_status()

                data = response.json()

                # Extract the summary (first paragraph)
                if "extract" in data and data["extract"]:
                    return data["extract"].strip()
                else:
                    logger.warning(f"No extract found for Wikipedia page: {page_title}")

            except requests.exceptions.HTTPError as e:
                if e.response.status_code == 429:  # Rate limited
                    logger.warning(
                        f"Wikipedia API rate limited, falling back to web scraping for: {url}"
                    )
                    time.sleep(10)  # Wait longer before fallback
                    return self.extract_web_content(url)
                else:
                    raise e

            # If API worked but no content, try web scraping as fallback
            logger.info(f"API returned no content, trying web scraping for: {url}")
            return self.extract_web_content(url)

        except Exception as e:
            logger.error(f"Error extracting Wikipedia content from {url}: {str(e)}")
            # Try web scraping as final fallback
            logger.info(f"Trying web scraping as fallback for: {url}")
            return self.extract_web_content(url)

    def extract_web_content(self, url: str) -> Optional[str]:
        """Extract first paragraph from web page using scraping"""
        try:
            response = self.session.get(url, timeout=10)
            response.raise_for_status()

            soup = BeautifulSoup(response.content, "html.parser")

            # Remove script and style elements
            for script in soup(["script", "style"]):
                script.decompose()

            # Try different strategies to find the first paragraph
            first_paragraph = None

            # Special handling for namu.wiki
            if "namu.wiki" in url:
                # Get all text and look for the main definition
                page_text = soup.get_text()

                # Look for the specific definition pattern we know exists
                # For 제과점, we expect: "제과점(製菓店)은 밀가루 디저트와 빵을 판매하는 가게를 말한다."
                if "제과점" in page_text:
                    # Find the definition line
                    pattern = r"제과점[^.]*을 말한다\."
                    match = re.search(pattern, page_text)
                    if match:
                        first_paragraph = match.group(0)

                # If specific pattern not found, look for general definition patterns
                if not first_paragraph:
                    lines = [
                        line.strip() for line in page_text.split("\n") if line.strip()
                    ]

                    for line in lines:
                        # Look for definition patterns (ends with "을 말한다", "이다", etc.)
                        if (
                            len(line) > 30
                            and (
                                "을 말한다." in line
                                or "를 말한다." in line
                                or (
                                    line.endswith("이다.")
                                    and ("은" in line or "는" in line)
                                )
                            )
                            and not line.startswith(
                                (
                                    "이 저작물은",
                                    "나무위키는",
                                    "기여하신",
                                    "분류",
                                    "최근 수정",
                                    "편집",
                                    "토론",
                                    "역사",
                                )
                            )
                            and "CC BY-NC-SA" not in line
                            and "라이선스" not in line
                            and "reCAPTCHA" not in line
                            and "Privacy Policy" not in line
                            and "Terms of Service" not in line
                        ):
                            first_paragraph = line
                            break

                # If no definition pattern found, look for substantial content
                if not first_paragraph:
                    for line in lines:
                        if (
                            len(line) > 100
                            and not line.startswith(
                                (
                                    "이 저작물은",
                                    "나무위키는",
                                    "기여하신",
                                    "분류",
                                    "최근 수정",
                                    "편집",
                                    "토론",
                                    "역사",
                                )
                            )
                            and "CC BY-NC-SA" not in line
                            and "라이선스" not in line
                            and "편집 요청" not in line
                            and "reCAPTCHA" not in line
                            and "Privacy Policy" not in line
                            and "Terms of Service" not in line
                            and any(char.isalpha() for char in line[:50])
                        ):
                            first_paragraph = line
                            break

            # Strategy 1: Look for main content areas (common in Korean wikis and Wikipedia)
            if not first_paragraph:
                content_selectors = [
                    ".mw-parser-output p",  # Wikipedia main content
                    ".wiki-content p",
                    ".content p",
                    ".article-content p",
                    ".main-content p",
                    "#content p",
                    ".entry-content p",
                    "#mw-content-text p",  # Wikipedia alternative
                    ".mw-content-ltr p",  # Wikipedia alternative
                ]

                for selector in content_selectors:
                    paragraphs = soup.select(selector)
                    if paragraphs:
                        for p in paragraphs:
                            text = p.get_text().strip()
                            # Filter out common footer/license content
                            if (
                                len(text) > 50
                                and not text.startswith(
                                    "이 저작물은"
                                )  # Korean license text
                                and not text.startswith(
                                    "나무위키는"
                                )  # Namu.wiki disclaimer
                                and not text.startswith("기여하신")  # Contribution text
                                and "CC BY-NC-SA" not in text  # License text
                                and "라이선스" not in text
                            ):  # License text
                                first_paragraph = text
                                break
                        if first_paragraph:
                            break

            # Strategy 2: If no specific content area found, look for any substantial paragraph
            if not first_paragraph:
                paragraphs = soup.find_all("p")
                for p in paragraphs:
                    text = p.get_text().strip()
                    # Filter out common footer/license content
                    if (
                        len(text) > 50
                        and not text.startswith("이 저작물은")  # Korean license text
                        and not text.startswith("나무위키는")  # Namu.wiki disclaimer
                        and not text.startswith("기여하신")  # Contribution text
                        and "CC BY-NC-SA" not in text  # License text
                        and "라이선스" not in text
                    ):  # License text
                        first_paragraph = text
                        break

            # Clean up the text
            if first_paragraph:
                # Remove extra whitespace and newlines
                first_paragraph = re.sub(r"\s+", " ", first_paragraph).strip()
                # Limit length to reasonable size
                if len(first_paragraph) > 1000:
                    first_paragraph = first_paragraph[:1000] + "..."

                return first_paragraph
            else:
                logger.warning(f"No substantial paragraph found for URL: {url}")
                return None

        except Exception as e:
            logger.error(f"Error extracting web content from {url}: {str(e)}")
            return None

    def extract_knowledge_point(self, source_url: str) -> Optional[str]:
        """Extract knowledge point based on source type"""
        if not source_url or pd.isna(source_url):
            return None

        source_url = str(source_url).strip()

        if "wikipedia" in source_url.lower():
            logger.info(f"Extracting from Wikipedia: {source_url}")
            return self.extract_wikipedia_content(source_url)
        else:
            logger.info(f"Extracting from web source: {source_url}")
            return self.extract_web_content(source_url)

    def process_csv(self, output_file: str = None):
        """Process the CSV file and add Knowledge Point column"""
        try:
            # Read the CSV file
            logger.info(f"Reading CSV file: {self.csv_file_path}")
            df = pd.read_csv(self.csv_file_path)

            # Check if Knowledge Point Source column exists
            if "Knowledge Point Source" not in df.columns:
                logger.error("'Knowledge Point Source' column not found in CSV")
                return

            # Add Knowledge Point column if it doesn't exist
            if "Knowledge Point" not in df.columns:
                df["Knowledge Point"] = ""

            total_rows = len(df)
            logger.info(f"Processing {total_rows} rows...")

            # Process each row
            for index, row in df.iterrows():
                logger.info(f"Processing row {index + 1}/{total_rows}")

                source_url = row["Knowledge Point Source"]

                # Skip if already processed or no source
                if (
                    pd.notna(row.get("Knowledge Point", ""))
                    and row.get("Knowledge Point", "").strip()
                ):
                    logger.info(
                        f"Row {index + 1} already has knowledge point, skipping"
                    )
                    continue

                # Extract knowledge point
                knowledge_point = self.extract_knowledge_point(source_url)

                if knowledge_point:
                    df.at[index, "Knowledge Point"] = knowledge_point
                    logger.info(
                        f"Successfully extracted knowledge point for row {index + 1}"
                    )
                else:
                    df.at[index, "Knowledge Point"] = "Content extraction failed"
                    logger.warning(
                        f"Failed to extract knowledge point for row {index + 1}"
                    )

                # Add delay to be respectful to servers
                time.sleep(5)

                # Save progress every 10 rows
                if (index + 1) % 10 == 0:
                    output_path = output_file or self.csv_file_path.replace(
                        ".csv", "_with_knowledge_points.csv"
                    )
                    df.to_csv(output_path, index=False)
                    logger.info(f"Progress saved at row {index + 1}")

            # Save final result
            output_path = output_file or self.csv_file_path.replace(
                ".csv", "_with_knowledge_points.csv"
            )
            df.to_csv(output_path, index=False)
            logger.info(f"Processing complete! Output saved to: {output_path}")

        except Exception as e:
            logger.error(f"Error processing CSV: {str(e)}")
            raise

    def process_csv_test(self, output_file: str = None):
        """Process only the first 5 rows for testing"""
        try:
            # Read the CSV file
            logger.info(f"Reading CSV file for testing: {self.csv_file_path}")
            df = pd.read_csv(self.csv_file_path)

            # Check if Knowledge Point Source column exists
            if "Knowledge Point Source" not in df.columns:
                logger.error("'Knowledge Point Source' column not found in CSV")
                return

            # Take first 5 rows for testing
            df = df.head(5)

            # Add Knowledge Point column if it doesn't exist
            if "Knowledge Point" not in df.columns:
                df["Knowledge Point"] = ""

            total_rows = len(df)
            logger.info(f"Testing with {total_rows} rows...")

            # Process each row
            for index, row in df.iterrows():
                logger.info(f"Processing test row {index + 1}/{total_rows}")

                source_url = row["Knowledge Point Source"]

                # Extract knowledge point
                knowledge_point = self.extract_knowledge_point(source_url)

                if knowledge_point:
                    df.at[index, "Knowledge Point"] = knowledge_point
                    logger.info(
                        f"Successfully extracted knowledge point for test row {index + 1}"
                    )
                else:
                    df.at[index, "Knowledge Point"] = "Content extraction failed"
                    logger.warning(
                        f"Failed to extract knowledge point for test row {index + 1}"
                    )

                # Add delay to be respectful to servers
                time.sleep(5)

            # Save test result
            output_path = output_file or self.csv_file_path.replace(
                ".csv", "_test_knowledge_points.csv"
            )
            df.to_csv(output_path, index=False)
            logger.info(f"Test processing complete! Output saved to: {output_path}")

        except Exception as e:
            logger.error(f"Error processing CSV in test mode: {str(e)}")
            raise


def main():
    """Main function"""
    import argparse

    parser = argparse.ArgumentParser(
        description="Extract knowledge points from CSV sources"
    )
    parser.add_argument(
        "--csv-file",
        default="/mnt/raid6/junkim100/east-asia/FINAL.csv",
        help="Path to the CSV file",
    )
    parser.add_argument(
        "--test", action="store_true", help="Test mode - process only first 5 rows"
    )
    parser.add_argument("--output", help="Output file path")

    args = parser.parse_args()

    extractor = KnowledgePointExtractor(args.csv_file)

    if args.test:
        # Test mode - process only first few rows
        extractor.process_csv_test(args.output)
    else:
        extractor.process_csv(args.output)


if __name__ == "__main__":
    main()
