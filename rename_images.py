#!/usr/bin/env python3
"""
<PERSON>ript to rename downloaded images to match the expected naming pattern for VQA generator.
"""

import csv
import os
import glob
import shutil

def rename_images():
    """Rename images from generic names to keyword-based names"""
    
    # Read the CSV file to get the mapping
    csv_file = "VQA.csv"
    image_dir = "downloaded_images"
    
    if not os.path.exists(csv_file):
        print(f"Error: {csv_file} not found")
        return
    
    if not os.path.exists(image_dir):
        print(f"Error: {image_dir} directory not found")
        return
    
    # Read CSV and create mapping
    with open(csv_file, 'r', encoding='utf-8') as f:
        reader = csv.DictReader(f)
        
        for row_num, row in enumerate(reader, start=2):  # Start from 2 since row 1 is header
            keyword = row['Keyword/Concept']
            
            # Find current image file
            current_patterns = [
                f"row_{row_num:02d}_keyword_{row_num}.jpg",
                f"row_{row_num}_keyword_{row_num}.jpg",
            ]
            
            current_file = None
            for pattern in current_patterns:
                full_path = os.path.join(image_dir, pattern)
                if os.path.exists(full_path):
                    current_file = full_path
                    break
            
            if not current_file:
                print(f"Warning: No image found for row {row_num} ({keyword})")
                continue
            
            # Create safe filename from keyword
            safe_keyword = "".join(
                c for c in keyword if c.isalnum() or c in (" ", "-", "_")
            ).rstrip()
            safe_keyword = safe_keyword.replace(" ", "_")
            
            # New filename
            new_filename = f"row_{row_num:02d}_{safe_keyword}.jpg"
            new_path = os.path.join(image_dir, new_filename)
            
            # Rename the file
            try:
                shutil.move(current_file, new_path)
                print(f"Renamed: {os.path.basename(current_file)} -> {new_filename}")
            except Exception as e:
                print(f"Error renaming {current_file}: {e}")

if __name__ == "__main__":
    rename_images()
